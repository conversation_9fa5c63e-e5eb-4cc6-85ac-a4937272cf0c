defmodule MqttableWeb.UnifiedPayloadEditorComponent do
  @moduledoc """
  Unified payload editor component with tab navigation and side-by-side layout.

  Features:
  - DaisyUI tabs for Text/JSON/Hex/File format selection
  - Side-by-side layout: Payload input (left) and Live preview (right)
  - Real-time preview updates
  - File upload support
  - Template evaluation
  """

  use MqttableWeb, :live_component
  alias Mqttable.Templating.Engine

  @impl true
  def mount(socket) do
    socket =
      socket
      |> assign_new(:payload, fn -> "" end)
      |> assign_new(:payload_format, fn -> "text" end)
      |> assign_new(:uploaded_file, fn -> nil end)
      |> assign_new(:file_upload_error, fn -> nil end)

    {:ok, socket}
  end

  @impl true
  def update(assigns, socket) do
    socket = assign(socket, assigns)
    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div
      class="form-control w-full"
      phx-hook="UnifiedPayloadEditor"
      id={"unified-payload-editor-container-#{@myself}"}
    >
      <!-- Tab Navigation -->
      <div class="tabs tabs-lifted mb-0">
        <button
          type="button"
          class={[
            "tab",
            if(@payload_format == "text" || @payload_format == nil, do: "tab-active", else: "")
          ]}
          phx-click="format_changed"
          phx-value-format="text"
          phx-target={@myself}
        >
          Text
        </button>
        <button
          type="button"
          class={["tab", if(@payload_format == "json", do: "tab-active", else: "")]}
          phx-click="format_changed"
          phx-value-format="json"
          phx-target={@myself}
        >
          JSON
        </button>
        <button
          type="button"
          class={["tab", if(@payload_format == "hex", do: "tab-active", else: "")]}
          phx-click="format_changed"
          phx-value-format="hex"
          phx-target={@myself}
        >
          Hex
        </button>
        <button
          type="button"
          class={["tab", if(@payload_format == "file", do: "tab-active", else: "")]}
          phx-click="format_changed"
          phx-value-format="file"
          phx-target={@myself}
        >
          File
        </button>
      </div>
      
    <!-- Main Content Area: Two-panel layout -->
      <div class="grid grid-cols-2 gap-0 border border-base-300 rounded-b-lg overflow-hidden">
        <!-- Left Panel: Payload Input -->
        <div class="col-span-1 border-r border-base-300">
          <div class="bg-base-200 px-3 py-2 border-b border-base-300">
            <span class="text-sm font-medium">Payload</span>
          </div>
          <div class="h-64">
            <%= if @payload_format == "file" do %>
              <!-- File Upload Area -->
              <div class="h-full flex flex-col">
                <!-- Hidden payload input for JavaScript to update -->
                <textarea name="payload" value={@payload} class="hidden"><%= @payload %></textarea>
                
    <!-- File Upload Interface -->
                <div class="flex-1 p-4">
                  <%= if @uploaded_file do %>
                    <!-- File Preview -->
                    <div class="bg-base-100 border border-base-300 rounded-lg p-4 h-full">
                      <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center gap-2">
                          <svg class="size-5 text-primary" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" />
                          </svg>
                          <span class="text-sm font-medium">{@uploaded_file.name}</span>
                        </div>
                        <button
                          type="button"
                          class="btn btn-ghost btn-xs"
                          phx-click="remove_file"
                          phx-target={@myself}
                        >
                          ✕
                        </button>
                      </div>

                      <%= if String.starts_with?(@uploaded_file.type, "image/") do %>
                        <img
                          src={@uploaded_file.data_url}
                          alt="Preview"
                          class="max-w-full max-h-32 object-contain rounded"
                        />
                      <% else %>
                        <div class="text-xs text-base-content/60">
                          <p>Type: {@uploaded_file.type}</p>
                          <p>Size: {format_file_size(@uploaded_file.size)}</p>
                        </div>
                      <% end %>
                    </div>
                  <% else %>
                    <!-- File Upload Drop Zone -->
                    <div class="border-2 border-dashed border-base-300 rounded-lg h-full flex flex-col items-center justify-center p-4 hover:border-primary transition-colors">
                      <svg
                        class="size-8 text-base-content/40 mb-2"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"
                        />
                      </svg>
                      <p class="text-sm font-medium mb-1">Choose a file to upload</p>
                      <p class="text-xs text-base-content/60 mb-3">Maximum size: 16MB</p>
                      <input
                        type="file"
                        class="file-input file-input-bordered file-input-primary file-input-sm w-full max-w-xs"
                        id={"file-upload-input-#{@myself}"}
                        phx-hook="FileUpload"
                        phx-target={@myself}
                      />
                    </div>
                  <% end %>
                  
    <!-- File Upload Errors -->
                  <%= if @file_upload_error do %>
                    <div class="alert alert-error mt-3 text-xs">
                      {@file_upload_error}
                    </div>
                  <% end %>
                </div>
              </div>
            <% else %>
              <!-- Text/JSON/Hex Input -->
              <textarea
                id={"payload-editor-#{@myself}"}
                name="payload"
                value={@payload}
                placeholder={get_payload_placeholder(@payload_format)}
                class="textarea w-full h-64 border-0 rounded-none resize-none focus:outline-none font-mono text-sm"
                spellcheck="false"
              ><%= @payload %></textarea>
              
    <!-- Hidden format input -->
              <input type="hidden" name="payload_format" value={@payload_format} />
            <% end %>
          </div>
        </div>
        
    <!-- Right Panel: Live Preview -->
        <div class="col-span-1">
          <div class="bg-base-200 px-3 py-2 border-b border-base-300">
            <span class="text-sm font-medium">Live Preview</span>
          </div>
          <div class="h-64 overflow-y-auto p-3">
            <.live_preview_content
              payload={@payload}
              payload_format={@payload_format}
              active_broker_name={Map.get(assigns, :active_broker_name, "")}
              uploaded_file={@uploaded_file}
            />
          </div>
        </div>
      </div>
    </div>
    """
  end

  # Event Handlers

  @impl true
  def handle_event("format_changed", %{"format" => format}, socket) do
    socket =
      socket
      |> assign(:payload_format, format)
      |> assign(:uploaded_file, nil)
      |> assign(:file_upload_error, nil)
      |> assign(:payload, if(format == "file", do: "", else: socket.assigns.payload))

    {:noreply, socket}
  end

  @impl true
  def handle_event("payload_changed", %{"value" => payload}, socket) do
    socket = assign(socket, :payload, payload)
    {:noreply, socket}
  end

  @impl true
  def handle_event("payload_changed", %{"payload" => payload}, socket) do
    socket = assign(socket, :payload, payload)
    {:noreply, socket}
  end

  @impl true
  def handle_event("remove_file", _params, socket) do
    socket =
      socket
      |> assign(:uploaded_file, nil)
      |> assign(:payload, "")
      |> assign(:file_upload_error, nil)

    # Notify parent component
    send(self(), {:payload_changed, ""})

    {:noreply, socket}
  end

  # Helper Functions

  defp get_payload_placeholder("text"), do: "Enter your message payload..."
  defp get_payload_placeholder("json"), do: ~s({"key": "value"})
  defp get_payload_placeholder("hex"), do: "48656C6C6F20576F726C64"
  defp get_payload_placeholder(_), do: "Enter your message payload..."

  defp format_file_size(size) when size < 1024, do: "#{size} B"
  defp format_file_size(size) when size < 1024 * 1024, do: "#{Float.round(size / 1024, 1)} KB"
  defp format_file_size(size), do: "#{Float.round(size / (1024 * 1024), 1)} MB"

  # Live Preview Component
  attr :payload, :string, required: true
  attr :payload_format, :string, required: true
  attr :active_broker_name, :string, default: ""
  attr :uploaded_file, :map, default: nil

  def live_preview_content(assigns) do
    # Generate basic preview result (compatible with original format)
    preview_result = generate_live_preview(assigns.payload, assigns.active_broker_name)

    # Generate format info separately
    format_info = get_format_info(assigns.payload_format, assigns.payload, assigns.uploaded_file)

    assigns =
      assigns
      |> assign(:preview_result, preview_result)
      |> assign(:format_info, format_info)

    ~H"""
    <%= if String.trim(@payload) == "" and @uploaded_file == nil do %>
      <div class="flex flex-col items-center justify-center h-full text-base-content/40">
        <svg class="size-12 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            d="M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m6.75 12H9m1.5-12H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"
          />
        </svg>
        <p class="text-sm font-medium">Preview will appear here</p>
        <p class="text-xs">Enter payload or upload file to see preview</p>
      </div>
    <% else %>
      <%= if @format_info do %>
        <div class="text-xs text-base-content/60 mb-2 border-b border-base-300 pb-2">
          {@format_info}
        </div>
      <% end %>

      <%= case @preview_result do %>
        <% {:ok, result} -> %>
          <.render_preview_by_format result={result} format={@payload_format} />
        <% {:error, error} -> %>
          <pre class="whitespace-pre-wrap text-error text-sm font-mono"><%= error %></pre>
      <% end %>
    <% end %>
    """
  end

  # Render preview based on format
  attr :result, :string, required: true
  attr :format, :string, required: true

  def render_preview_by_format(assigns) do
    ~H"""
    <%= case @format do %>
      <% "json" -> %>
        <.render_json_preview content={@result} />
      <% "hex" -> %>
        <.render_hex_preview content={@result} />
      <% "file" -> %>
        <pre class="whitespace-pre-wrap text-info text-sm font-mono"><%= @result %></pre>
      <% _ -> %>
        <pre class="whitespace-pre-wrap text-success text-sm font-mono"><%= @result %></pre>
    <% end %>
    """
  end

  # JSON Preview with syntax highlighting
  attr :content, :string, required: true

  def render_json_preview(assigns) do
    ~H"""
    <div class="bg-base-100 border border-base-300 rounded p-2">
      <%= case Jason.decode(@content) do %>
        <% {:ok, decoded} -> %>
          <pre class="whitespace-pre-wrap text-success text-xs font-mono"><%= Jason.encode!(decoded, pretty: true) %></pre>
        <% {:error, _} -> %>
          <pre class="whitespace-pre-wrap text-warning text-xs font-mono"><%= @content %></pre>
          <div class="text-error text-xs mt-1">⚠️ Invalid JSON format</div>
      <% end %>
    </div>
    """
  end

  # Hex Preview with formatting
  attr :content, :string, required: true

  def render_hex_preview(assigns) do
    formatted_hex = format_hex_display(assigns.content)
    assigns = assign(assigns, :formatted_hex, formatted_hex)

    ~H"""
    <div class="bg-base-100 border border-base-300 rounded p-2">
      <pre class="whitespace-pre-wrap text-primary text-xs font-mono"><%= @formatted_hex %></pre>
    </div>
    """
  end

  defp format_hex_display(hex_string) do
    hex_string
    |> String.replace(~r/[^0-9A-Fa-f]/, "")
    |> String.upcase()
    |> String.graphemes()
    |> Enum.chunk_every(2)
    |> Enum.map(&Enum.join/1)
    |> Enum.chunk_every(8)
    |> Enum.map(&Enum.join(&1, " "))
    |> Enum.join("\n")
  end

  # Keep the original generate_live_preview interface for compatibility
  defp generate_live_preview(payload, active_broker_name) do
    if String.contains?(payload, "{{") || String.contains?(payload, "{%") do
      # Get broker variables if broker_name is provided
      variables = get_broker_variables(active_broker_name)

      case Engine.render(payload, %{}, variables) do
        {:ok, result} -> {:ok, result}
        {:error, error} -> {:error, inspect(error)}
      end
    else
      {:ok, payload}
    end
  end

  # Generate format info for display
  defp get_format_info(payload_format, payload, uploaded_file) do
    cond do
      uploaded_file != nil ->
        "File: #{uploaded_file.name} (#{format_file_size(uploaded_file.size)})"

      payload_format == "json" ->
        case Jason.decode(payload) do
          {:ok, _} -> "Valid JSON (#{String.length(payload)} chars)"
          {:error, _} -> "Invalid JSON format"
        end

      payload_format == "hex" ->
        clean_hex = String.replace(payload, ~r/[^0-9A-Fa-f]/, "")
        byte_count = div(String.length(clean_hex), 2)
        "Hex data (#{byte_count} bytes)"

      payload_format == "text" ->
        "Text (#{String.length(payload)} chars)"

      payload_format == "file" ->
        "File content"

      true ->
        "#{String.length(payload)} characters"
    end
  end

  defp get_broker_variables(nil), do: %{}
  defp get_broker_variables(""), do: %{}

  defp get_broker_variables(broker_name) do
    # Get broker variables from connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    case Enum.find(connection_sets, fn cs -> cs.name == broker_name end) do
      nil -> %{}
      connection_set -> Map.get(connection_set, :variables, %{})
    end
  end
end
